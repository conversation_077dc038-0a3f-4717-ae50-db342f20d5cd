import React, { useState, useEffect } from 'react';
import { RefreshCw, ExternalLink, AlertCircle } from 'lucide-react';

function PreviewFrame({ url }) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUrl, setLastUrl] = useState(null);

  useEffect(() => {
    if (url && url !== lastUrl) {
      setIsLoading(true);
      setError(null);
      setLastUrl(url);
    }
  }, [url, lastUrl]);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setError('Failed to load preview');
  };

  const handleRefresh = () => {
    setIsLoading(true);
    setError(null);
    // Force iframe reload by changing src
    const iframe = document.getElementById('preview-iframe');
    if (iframe && url) {
      iframe.src = url + '?t=' + Date.now();
    }
  };

  const handleOpenInNewTab = () => {
    if (url) {
      window.open(url, '_blank');
    }
  };

  if (!url) {
    return (
      <div className="preview-container">
        <div className="loading-container">
          <div className="text-lg font-bold">No Preview Available</div>
          <div className="text-sm" style={{ color: '#888' }}>
            Start your project to see the live preview here.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="preview-container" style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* Preview Header */}
      <div style={{
        height: '40px',
        backgroundColor: '#252525',
        borderBottom: '1px solid #2d2d2d',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '0 12px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '13px', color: '#cccccc' }}>Preview</span>
          {isLoading && <div className="spinner" />}
          {error && <AlertCircle size={14} color="#ff6b6b" />}
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
          <button
            className="btn btn-secondary"
            onClick={handleRefresh}
            title="Refresh Preview"
            style={{ padding: '4px 8px' }}
          >
            <RefreshCw size={14} />
          </button>
          
          <button
            className="btn btn-secondary"
            onClick={handleOpenInNewTab}
            title="Open in New Tab"
            style={{ padding: '4px 8px' }}
          >
            <ExternalLink size={14} />
          </button>
        </div>
      </div>

      {/* Preview Content */}
      <div style={{ flex: 1, position: 'relative', backgroundColor: '#ffffff' }}>
        {error ? (
          <div className="loading-container">
            <AlertCircle size={48} color="#ff6b6b" />
            <div className="text-lg font-bold" style={{ color: '#ff6b6b' }}>Preview Error</div>
            <div className="text-sm" style={{ color: '#888' }}>
              {error}
            </div>
            <button className="btn btn-primary" onClick={handleRefresh}>
              Try Again
            </button>
          </div>
        ) : (
          <>
            {isLoading && (
              <div 
                style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: 10
                }}
              >
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '12px' }}>
                  <div className="spinner" style={{ width: '32px', height: '32px' }} />
                  <div style={{ color: '#666' }}>Loading preview...</div>
                </div>
              </div>
            )}
            
            <iframe
              id="preview-iframe"
              src={url}
              className="preview-iframe"
              onLoad={handleIframeLoad}
              onError={handleIframeError}
              title="Project Preview"
              sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                backgroundColor: 'white'
              }}
            />
          </>
        )}
      </div>
    </div>
  );
}

export default PreviewFrame;
