import React, { useState } from 'react';
import Header from './components/Layout/Header';
import Sidebar from './components/Layout/Sidebar';
import MainEditor from './components/Editor/MainEditor';
import ChatInterface from './components/Chat/ChatInterface';
import ProjectProvider from './context/ProjectContext';
import './App.css';

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [chatOpen, setChatOpen] = useState(true);

  return (
    <ProjectProvider>
      <div className="app">
        <Header 
          onToggleSidebar={() => setSidebarOpen(!sidebarOpen)}
          onToggleChat={() => setChatOpen(!chatOpen)}
          sidebarOpen={sidebarOpen}
          chatOpen={chatOpen}
        />
        
        <div className="app-body">
          {sidebarOpen && (
            <Sidebar />
          )}
          
          <div className="main-content">
            <MainEditor />
          </div>
          
          {chatOpen && (
            <ChatInterface />
          )}
        </div>
      </div>
    </ProjectProvider>
  );
}

export default App;
