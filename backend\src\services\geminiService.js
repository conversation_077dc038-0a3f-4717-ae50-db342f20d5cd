const { GoogleGenerativeAI } = require('@google/generative-ai');

class GeminiService {
  constructor() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY is required');
    }
    
    this.genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-2.0-flash-exp' });
  }

  async generateCode(prompt, context = {}) {
    try {
      const enhancedPrompt = this.buildPrompt(prompt, context);
      
      const result = await this.model.generateContent(enhancedPrompt);
      const response = await result.response;
      const text = response.text();
      
      return this.parseCodeResponse(text);
    } catch (error) {
      console.error('Gemini API Error:', error);
      throw new Error(`Code generation failed: ${error.message}`);
    }
  }

  buildPrompt(userPrompt, context) {
    const systemPrompt = `You are an expert full-stack developer. Generate complete, production-ready code based on user requirements.

IMPORTANT RULES:
1. Generate COMPLETE project files with all necessary code
2. Include package.json with all required dependencies
3. Use modern best practices and latest versions
4. Generate multiple files when needed (components, utils, styles, etc.)
5. Include proper error handling and validation
6. Make code production-ready, not just examples
7. Use TypeScript when appropriate
8. Include proper file structure and organization

RESPONSE FORMAT:
Return your response as a JSON object with this exact structure:
{
  "projectType": "react|nextjs|nodejs|vanilla|vue|angular",
  "description": "Brief description of the generated project",
  "files": [
    {
      "path": "relative/file/path.js",
      "content": "complete file content here",
      "type": "file"
    }
  ],
  "dependencies": {
    "package.json dependencies": "versions"
  },
  "devDependencies": {
    "package.json devDependencies": "versions"
  },
  "scripts": {
    "npm scripts": "commands"
  },
  "instructions": [
    "Step by step setup instructions"
  ]
}

User Request: ${userPrompt}

${context.framework ? `Preferred Framework: ${context.framework}` : ''}
${context.styling ? `Styling Preference: ${context.styling}` : ''}
${context.features ? `Required Features: ${context.features.join(', ')}` : ''}`;

    return systemPrompt;
  }

  parseCodeResponse(response) {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) || 
                       response.match(/```\n([\s\S]*?)\n```/) ||
                       response.match(/\{[\s\S]*\}/);
      
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];
        return JSON.parse(jsonStr);
      }
      
      // Fallback: try to parse the entire response as JSON
      return JSON.parse(response);
    } catch (error) {
      console.error('Failed to parse Gemini response:', error);
      
      // Fallback: return a basic structure
      return {
        projectType: 'react',
        description: 'Generated project',
        files: [
          {
            path: 'src/App.js',
            content: response,
            type: 'file'
          }
        ],
        dependencies: {},
        devDependencies: {},
        scripts: {},
        instructions: ['Install dependencies with npm install', 'Run with npm start']
      };
    }
  }

  async generateProjectStructure(description, options = {}) {
    const prompt = `Generate a complete project structure for: ${description}
    
    Options:
    - Framework: ${options.framework || 'React'}
    - Styling: ${options.styling || 'CSS'}
    - Features: ${options.features ? options.features.join(', ') : 'Basic functionality'}
    
    Create a full project with proper file organization, components, and functionality.`;
    
    return this.generateCode(prompt, options);
  }
}

module.exports = new GeminiService();
