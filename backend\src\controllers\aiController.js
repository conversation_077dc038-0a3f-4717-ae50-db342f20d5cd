const codeGenerationService = require('../services/codeGenerationService');

class AIController {
  async generateProject(req, res) {
    try {
      const { description, framework, styling, features } = req.body;

      if (!description) {
        return res.status(400).json({
          error: 'Description is required',
          message: 'Please provide a description of what you want to build'
        });
      }

      console.log('Generating project for:', description);

      const userInput = {
        description,
        framework,
        styling,
        features
      };

      const generatedProject = await codeGenerationService.generateProject(userInput);

      res.json({
        success: true,
        project: generatedProject,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Project generation error:', error);
      res.status(500).json({
        error: 'Project generation failed',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async generateFile(req, res) {
    try {
      const { prompt, fileType } = req.body;

      if (!prompt) {
        return res.status(400).json({
          error: 'Prompt is required',
          message: 'Please provide a prompt for file generation'
        });
      }

      console.log('Generating file for:', prompt);

      const result = await codeGenerationService.generateSingleFile(prompt, fileType);

      res.json({
        success: true,
        file: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('File generation error:', error);
      res.status(500).json({
        error: 'File generation failed',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async enhanceCode(req, res) {
    try {
      const { code, enhancement } = req.body;

      if (!code || !enhancement) {
        return res.status(400).json({
          error: 'Code and enhancement description are required'
        });
      }

      console.log('Enhancing code with:', enhancement);

      const prompt = `Enhance this code based on the following request: ${enhancement}

Current code:
${code}

Return the enhanced version with improvements.`;

      const result = await codeGenerationService.generateSingleFile(prompt, 'enhancement');

      res.json({
        success: true,
        enhancedCode: result.content,
        suggestions: result.suggestions,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Code enhancement error:', error);
      res.status(500).json({
        error: 'Code enhancement failed',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async explainCode(req, res) {
    try {
      const { code } = req.body;

      if (!code) {
        return res.status(400).json({
          error: 'Code is required for explanation'
        });
      }

      console.log('Explaining code...');

      const prompt = `Explain this code in detail. Break down what it does, how it works, and any important concepts:

${code}

Provide a clear, educational explanation.`;

      const result = await codeGenerationService.generateSingleFile(prompt, 'explanation');

      res.json({
        success: true,
        explanation: result.content,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Code explanation error:', error);
      res.status(500).json({
        error: 'Code explanation failed',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async getSupportedFrameworks(req, res) {
    try {
      const frameworks = [
        { id: 'react', name: 'React', description: 'Popular JavaScript library for building user interfaces' },
        { id: 'nextjs', name: 'Next.js', description: 'React framework with server-side rendering' },
        { id: 'vue', name: 'Vue.js', description: 'Progressive JavaScript framework' },
        { id: 'angular', name: 'Angular', description: 'Platform for building mobile and desktop web applications' },
        { id: 'nodejs', name: 'Node.js', description: 'JavaScript runtime for server-side applications' },
        { id: 'express', name: 'Express.js', description: 'Fast, unopinionated web framework for Node.js' },
        { id: 'vanilla', name: 'Vanilla HTML/CSS/JS', description: 'Pure HTML, CSS, and JavaScript' }
      ];

      const stylingOptions = [
        { id: 'css', name: 'CSS', description: 'Standard CSS styling' },
        { id: 'tailwind', name: 'Tailwind CSS', description: 'Utility-first CSS framework' },
        { id: 'styled-components', name: 'Styled Components', description: 'CSS-in-JS library' },
        { id: 'sass', name: 'Sass/SCSS', description: 'CSS preprocessor' },
        { id: 'css-modules', name: 'CSS Modules', description: 'Localized CSS' }
      ];

      res.json({
        success: true,
        frameworks,
        stylingOptions,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Error fetching supported frameworks:', error);
      res.status(500).json({
        error: 'Failed to fetch supported frameworks',
        message: error.message
      });
    }
  }
}

module.exports = new AIController();
