const express = require('express');
const projectController = require('../controllers/projectController');

const router = express.Router();

// Project CRUD operations
router.post('/', projectController.createProject);
router.get('/', projectController.listProjects);
router.get('/:projectId', projectController.getProject);
router.put('/:projectId', projectController.updateProject);
router.delete('/:projectId', projectController.deleteProject);

// File operations within projects
router.put('/:projectId/files', projectController.updateFile);
router.delete('/:projectId/files', projectController.deleteFile);

module.exports = router;
