import React, { useState } from 'react';
import { Folder, File, Plus, Trash2 } from 'lucide-react';
import { useProject } from '../../context/ProjectContext';

function Sidebar() {
  const { files, activeFile, setActiveFile, addFile, removeFile } = useProject();
  const [newFileName, setNewFileName] = useState('');
  const [showNewFileInput, setShowNewFileInput] = useState(false);

  const handleFileClick = (file) => {
    setActiveFile(file);
  };

  const handleAddFile = async () => {
    if (newFileName.trim()) {
      await addFile(newFileName.trim());
      setNewFileName('');
      setShowNewFileInput(false);
    }
  };

  const handleDeleteFile = async (filePath, e) => {
    e.stopPropagation();
    if (window.confirm(`Are you sure you want to delete ${filePath}?`)) {
      await removeFile(filePath);
    }
  };

  const getFileIcon = (filePath) => {
    const extension = filePath.split('.').pop()?.toLowerCase();
    
    // You can expand this with more specific icons
    switch (extension) {
      case 'js':
      case 'jsx':
      case 'ts':
      case 'tsx':
        return '📄';
      case 'css':
      case 'scss':
      case 'sass':
        return '🎨';
      case 'html':
        return '🌐';
      case 'json':
        return '⚙️';
      case 'md':
        return '📝';
      default:
        return '📄';
    }
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="flex items-center justify-between">
          <h3 className="font-bold">Files</h3>
          <button
            className="btn btn-secondary"
            onClick={() => setShowNewFileInput(true)}
            title="Add New File"
          >
            <Plus size={14} />
          </button>
        </div>
      </div>

      <div className="sidebar-content">
        {showNewFileInput && (
          <div className="flex gap-2 mb-4">
            <input
              type="text"
              className="input flex-1"
              placeholder="filename.js"
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleAddFile()}
              autoFocus
            />
            <button className="btn btn-primary" onClick={handleAddFile}>
              Add
            </button>
            <button 
              className="btn btn-secondary" 
              onClick={() => {
                setShowNewFileInput(false);
                setNewFileName('');
              }}
            >
              Cancel
            </button>
          </div>
        )}

        <div className="file-explorer">
          {files.length === 0 ? (
            <div className="text-center text-sm" style={{ color: '#888' }}>
              No files yet. Generate a project or add files manually.
            </div>
          ) : (
            files.map((file) => (
              <div
                key={file.path}
                className={`file-item ${activeFile?.path === file.path ? 'active' : ''}`}
                onClick={() => handleFileClick(file)}
              >
                <span className="file-icon">
                  {getFileIcon(file.path)}
                </span>
                <span className="flex-1">{file.path}</span>
                <button
                  className="btn btn-danger"
                  style={{ padding: '2px 6px', fontSize: '12px' }}
                  onClick={(e) => handleDeleteFile(file.path, e)}
                  title="Delete File"
                >
                  <Trash2 size={12} />
                </button>
              </div>
            ))
          )}
        </div>

        {files.length > 0 && (
          <div className="mt-4 p-3 bg-gray-800 rounded text-sm">
            <div className="font-bold mb-2">Project Info</div>
            <div>Files: {files.length}</div>
            <div>Active: {activeFile?.path || 'None'}</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Sidebar;
