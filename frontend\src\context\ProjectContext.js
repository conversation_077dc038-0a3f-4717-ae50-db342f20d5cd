import React, { createContext, useContext, useReducer, useEffect } from 'react';
// import { WebContainer } from '@webcontainer/api';
import apiService from '../services/apiService';

const ProjectContext = createContext();

const initialState = {
  currentProject: null,
  projects: [],
  activeFile: null,
  files: [],
  webContainer: null,
  isLoading: false,
  error: null,
  previewUrl: null,
  terminal: null
};

function projectReducer(state, action) {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_CURRENT_PROJECT':
      return { ...state, currentProject: action.payload };
    
    case 'SET_FILES':
      return { ...state, files: action.payload };
    
    case 'SET_ACTIVE_FILE':
      return { ...state, activeFile: action.payload };
    
    case 'UPDATE_FILE':
      return {
        ...state,
        files: state.files.map(file =>
          file.path === action.payload.path
            ? { ...file, content: action.payload.content }
            : file
        )
      };
    
    case 'ADD_FILE':
      return {
        ...state,
        files: [...state.files, action.payload]
      };
    
    case 'REMOVE_FILE':
      return {
        ...state,
        files: state.files.filter(file => file.path !== action.payload),
        activeFile: state.activeFile?.path === action.payload ? null : state.activeFile
      };
    
    case 'SET_WEBCONTAINER':
      return { ...state, webContainer: action.payload };
    
    case 'SET_PREVIEW_URL':
      return { ...state, previewUrl: action.payload };
    
    case 'SET_TERMINAL':
      return { ...state, terminal: action.payload };
    
    default:
      return state;
  }
}

export function ProjectProvider({ children }) {
  const [state, dispatch] = useReducer(projectReducer, initialState);

  // Initialize WebContainer (temporarily disabled for testing)
  useEffect(() => {
    // const initWebContainer = async () => {
    //   try {
    //     const webcontainerInstance = await WebContainer.boot();
    //     dispatch({ type: 'SET_WEBCONTAINER', payload: webcontainerInstance });

    //     // Listen for server ready
    //     webcontainerInstance.on('server-ready', (port, url) => {
    //       dispatch({ type: 'SET_PREVIEW_URL', payload: url });
    //     });

    //   } catch (error) {
    //     console.error('Failed to initialize WebContainer:', error);
    //     dispatch({ type: 'SET_ERROR', payload: 'Failed to initialize WebContainer' });
    //   }
    // };

    // initWebContainer();
  }, []);

  const generateProject = async (description, options = {}) => {
    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      const response = await apiService.generateProject({
        description,
        ...options
      });

      const project = response.project;
      
      dispatch({ type: 'SET_CURRENT_PROJECT', payload: project });
      dispatch({ type: 'SET_FILES', payload: project.files });
      
      // Set first file as active
      if (project.files.length > 0) {
        dispatch({ type: 'SET_ACTIVE_FILE', payload: project.files[0] });
      }

      // Load project into WebContainer
      if (state.webContainer) {
        await loadProjectIntoWebContainer(project);
      }

      return project;
    } catch (error) {
      console.error('Project generation failed:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  const loadProjectIntoWebContainer = async (project) => {
    if (!state.webContainer) return;

    try {
      // Create file system structure
      const fileSystem = {};
      
      project.files.forEach(file => {
        fileSystem[file.path] = {
          file: {
            contents: file.content
          }
        };
      });

      // Mount the file system
      await state.webContainer.mount(fileSystem);

      // Install dependencies if package.json exists
      const packageJson = project.files.find(f => f.path === 'package.json');
      if (packageJson) {
        const installProcess = await state.webContainer.spawn('npm', ['install']);
        await installProcess.exit;
      }

      // Start the development server
      const startCommand = getStartCommand(project.projectType);
      if (startCommand) {
        const serverProcess = await state.webContainer.spawn('npm', ['run', startCommand]);
        
        // Create terminal for output
        const terminal = {
          process: serverProcess,
          output: []
        };
        
        dispatch({ type: 'SET_TERMINAL', payload: terminal });
      }

    } catch (error) {
      console.error('Failed to load project into WebContainer:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load project' });
    }
  };

  const getStartCommand = (projectType) => {
    const commands = {
      'react': 'start',
      'nextjs': 'dev',
      'vue': 'serve',
      'nodejs': 'dev',
      'vanilla': 'start'
    };
    return commands[projectType] || 'start';
  };

  const updateFile = async (filePath, content) => {
    dispatch({ type: 'UPDATE_FILE', payload: { path: filePath, content } });

    // Update in WebContainer
    if (state.webContainer) {
      try {
        await state.webContainer.fs.writeFile(filePath, content);
      } catch (error) {
        console.error('Failed to update file in WebContainer:', error);
      }
    }
  };

  const addFile = async (filePath, content = '') => {
    const newFile = {
      path: filePath,
      content,
      type: 'file'
    };

    dispatch({ type: 'ADD_FILE', payload: newFile });

    // Add to WebContainer
    if (state.webContainer) {
      try {
        await state.webContainer.fs.writeFile(filePath, content);
      } catch (error) {
        console.error('Failed to add file to WebContainer:', error);
      }
    }
  };

  const removeFile = async (filePath) => {
    dispatch({ type: 'REMOVE_FILE', payload: filePath });

    // Remove from WebContainer
    if (state.webContainer) {
      try {
        await state.webContainer.fs.rm(filePath);
      } catch (error) {
        console.error('Failed to remove file from WebContainer:', error);
      }
    }
  };

  const setActiveFile = (file) => {
    dispatch({ type: 'SET_ACTIVE_FILE', payload: file });
  };

  const value = {
    ...state,
    generateProject,
    updateFile,
    addFile,
    removeFile,
    setActiveFile
  };

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
}

export function useProject() {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
}

export default ProjectProvider;
