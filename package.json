{"name": "ai-code-platform", "version": "1.0.0", "description": "AI-powered code generation platform with WebContainers", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["ai", "code-generation", "webcontainers", "gemini"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}