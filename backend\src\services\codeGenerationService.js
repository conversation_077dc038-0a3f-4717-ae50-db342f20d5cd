const geminiService = require('./geminiService');
const fileTemplates = require('../utils/fileTemplates');

class CodeGenerationService {
  constructor() {
    this.supportedFrameworks = [
      'react', 'nextjs', 'vue', 'angular', 'nodejs', 'express', 'vanilla'
    ];
  }

  async generateProject(userInput) {
    try {
      // Parse user input and determine project type
      const projectConfig = this.parseUserInput(userInput);
      
      // Generate code using Gemini
      const generatedProject = await geminiService.generateProjectStructure(
        projectConfig.description,
        projectConfig.options
      );

      // Enhance the generated project with additional files if needed
      const enhancedProject = await this.enhanceProject(generatedProject);

      // Validate and sanitize the project structure
      const validatedProject = this.validateProject(enhancedProject);

      return validatedProject;
    } catch (error) {
      console.error('Code generation error:', error);
      throw new Error(`Failed to generate project: ${error.message}`);
    }
  }

  parseUserInput(input) {
    const description = input.description || input;
    const framework = this.detectFramework(description) || input.framework || 'react';
    const styling = input.styling || this.detectStyling(description) || 'css';
    const features = input.features || this.extractFeatures(description);

    return {
      description,
      options: {
        framework,
        styling,
        features
      }
    };
  }

  detectFramework(description) {
    const text = description.toLowerCase();
    
    if (text.includes('next.js') || text.includes('nextjs')) return 'nextjs';
    if (text.includes('vue')) return 'vue';
    if (text.includes('angular')) return 'angular';
    if (text.includes('express') || text.includes('api') || text.includes('backend')) return 'nodejs';
    if (text.includes('react')) return 'react';
    if (text.includes('vanilla') || text.includes('html')) return 'vanilla';
    
    return 'react'; // default
  }

  detectStyling(description) {
    const text = description.toLowerCase();
    
    if (text.includes('tailwind')) return 'tailwind';
    if (text.includes('styled-components')) return 'styled-components';
    if (text.includes('sass') || text.includes('scss')) return 'sass';
    if (text.includes('css modules')) return 'css-modules';
    
    return 'css';
  }

  extractFeatures(description) {
    const features = [];
    const text = description.toLowerCase();
    
    if (text.includes('auth') || text.includes('login')) features.push('authentication');
    if (text.includes('database') || text.includes('db')) features.push('database');
    if (text.includes('api')) features.push('api');
    if (text.includes('responsive')) features.push('responsive');
    if (text.includes('dark mode')) features.push('dark-mode');
    if (text.includes('form')) features.push('forms');
    if (text.includes('chart') || text.includes('graph')) features.push('charts');
    if (text.includes('upload')) features.push('file-upload');
    
    return features;
  }

  async enhanceProject(project) {
    // Add essential files that might be missing
    const enhancedFiles = [...project.files];
    
    // Ensure package.json exists
    if (!enhancedFiles.find(f => f.path === 'package.json')) {
      enhancedFiles.push({
        path: 'package.json',
        content: JSON.stringify({
          name: 'generated-project',
          version: '1.0.0',
          private: true,
          dependencies: project.dependencies || {},
          devDependencies: project.devDependencies || {},
          scripts: project.scripts || fileTemplates.getDefaultScripts(project.projectType)
        }, null, 2),
        type: 'file'
      });
    }

    // Add README.md
    if (!enhancedFiles.find(f => f.path === 'README.md')) {
      enhancedFiles.push({
        path: 'README.md',
        content: fileTemplates.generateReadme(project),
        type: 'file'
      });
    }

    // Add .gitignore
    if (!enhancedFiles.find(f => f.path === '.gitignore')) {
      enhancedFiles.push({
        path: '.gitignore',
        content: fileTemplates.getGitignore(project.projectType),
        type: 'file'
      });
    }

    return {
      ...project,
      files: enhancedFiles
    };
  }

  validateProject(project) {
    // Ensure required fields exist
    const validated = {
      projectType: project.projectType || 'react',
      description: project.description || 'Generated project',
      files: project.files || [],
      dependencies: project.dependencies || {},
      devDependencies: project.devDependencies || {},
      scripts: project.scripts || {},
      instructions: project.instructions || []
    };

    // Validate file paths
    validated.files = validated.files.map(file => ({
      ...file,
      path: this.sanitizePath(file.path),
      content: file.content || '',
      type: file.type || 'file'
    }));

    // Ensure at least one file exists
    if (validated.files.length === 0) {
      validated.files.push({
        path: 'src/App.js',
        content: fileTemplates.getBasicComponent(validated.projectType),
        type: 'file'
      });
    }

    return validated;
  }

  sanitizePath(path) {
    // Remove any dangerous path components
    return path
      .replace(/\.\./g, '') // Remove parent directory references
      .replace(/^\/+/, '') // Remove leading slashes
      .replace(/\/+/g, '/') // Normalize multiple slashes
      .trim();
  }

  async generateSingleFile(prompt, fileType = 'component') {
    try {
      const enhancedPrompt = `Generate a single ${fileType} file based on this request: ${prompt}
      
      Return only the file content, no explanations or markdown formatting.`;
      
      const result = await geminiService.generateCode(enhancedPrompt);
      
      return {
        content: result.files?.[0]?.content || result,
        suggestions: result.instructions || []
      };
    } catch (error) {
      console.error('Single file generation error:', error);
      throw new Error(`Failed to generate file: ${error.message}`);
    }
  }
}

module.exports = new CodeGenerationService();
