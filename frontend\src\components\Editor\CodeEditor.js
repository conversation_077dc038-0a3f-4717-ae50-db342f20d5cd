import React, { useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { useProject } from '../../context/ProjectContext';

function CodeEditor({ file }) {
  const { updateFile } = useProject();
  const editorRef = useRef(null);

  const handleEditorDidMount = (editor, monaco) => {
    editorRef.current = editor;
    
    // Configure Monaco Editor theme
    monaco.editor.defineTheme('ai-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#1a1a1a',
        'editor.foreground': '#ffffff',
        'editorLineNumber.foreground': '#666666',
        'editorLineNumber.activeForeground': '#ffffff',
        'editor.selectionBackground': '#007acc40',
        'editor.inactiveSelectionBackground': '#007acc20',
        'editorCursor.foreground': '#ffffff',
        'editor.findMatchBackground': '#515c6a',
        'editor.findMatchHighlightBackground': '#ea5c0040',
        'editor.findRangeHighlightBackground': '#3a3d4166',
        'editorHoverWidget.background': '#252526',
        'editorHoverWidget.border': '#454545',
        'editorWidget.background': '#252526',
        'editorWidget.border': '#454545',
        'editorSuggestWidget.background': '#252526',
        'editorSuggestWidget.border': '#454545',
        'editorSuggestWidget.selectedBackground': '#094771',
      }
    });
    
    monaco.editor.setTheme('ai-dark');
  };

  const handleEditorChange = (value) => {
    if (file && value !== undefined) {
      updateFile(file.path, value);
    }
  };

  const getLanguage = (filePath) => {
    if (!filePath) return 'javascript';
    
    const extension = filePath.split('.').pop()?.toLowerCase();
    
    const languageMap = {
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'css': 'css',
      'scss': 'scss',
      'sass': 'sass',
      'html': 'html',
      'json': 'json',
      'md': 'markdown',
      'py': 'python',
      'java': 'java',
      'cpp': 'cpp',
      'c': 'c',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'sql': 'sql',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml'
    };

    return languageMap[extension] || 'javascript';
  };

  if (!file) {
    return (
      <div className="loading-container">
        <div>Select a file to start editing</div>
      </div>
    );
  }

  return (
    <div style={{ height: '100%', width: '100%' }}>
      <Editor
        height="100%"
        language={getLanguage(file.path)}
        value={file.content}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        options={{
          minimap: { enabled: true },
          fontSize: 14,
          lineNumbers: 'on',
          roundedSelection: false,
          scrollBeyondLastLine: false,
          automaticLayout: true,
          tabSize: 2,
          insertSpaces: true,
          wordWrap: 'on',
          contextmenu: true,
          selectOnLineNumbers: true,
          glyphMargin: true,
          folding: true,
          foldingStrategy: 'indentation',
          showFoldingControls: 'always',
          unfoldOnClickAfterEndOfLine: false,
          bracketPairColorization: {
            enabled: true
          },
          guides: {
            bracketPairs: true,
            indentation: true
          },
          suggest: {
            enabled: true,
            showKeywords: true,
            showSnippets: true
          },
          quickSuggestions: {
            other: true,
            comments: true,
            strings: true
          },
          parameterHints: {
            enabled: true
          },
          autoClosingBrackets: 'always',
          autoClosingQuotes: 'always',
          autoSurround: 'languageDefined',
          formatOnPaste: true,
          formatOnType: true
        }}
      />
    </div>
  );
}

export default CodeEditor;
