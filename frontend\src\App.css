.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #0f0f0f;
  color: #ffffff;
}

.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

/* Header Styles */
.header {
  height: 50px;
  background-color: #1a1a1a;
  border-bottom: 1px solid #2d2d2d;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: #007acc;
}

/* Sidebar Styles */
.sidebar {
  width: 300px;
  background-color: #1a1a1a;
  border-right: 1px solid #2d2d2d;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #2d2d2d;
  background-color: #252525;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

/* Chat Interface Styles */
.chat-interface {
  width: 350px;
  background-color: #1a1a1a;
  border-left: 1px solid #2d2d2d;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #2d2d2d;
  background-color: #252525;
  display: flex;
  align-items: center;
  justify-content: between;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-input-container {
  padding: 16px;
  border-top: 1px solid #2d2d2d;
  background-color: #252525;
}

/* Message Styles */
.message {
  padding: 12px;
  border-radius: 8px;
  max-width: 80%;
  word-wrap: break-word;
}

.message-user {
  background-color: #007acc;
  color: white;
  align-self: flex-end;
  margin-left: auto;
}

.message-ai {
  background-color: #2d2d2d;
  color: white;
  align-self: flex-start;
}

.message-system {
  background-color: #404040;
  color: #cccccc;
  align-self: center;
  font-style: italic;
  font-size: 12px;
}

/* Editor Styles */
.editor-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.editor-tabs {
  height: 40px;
  background-color: #252525;
  border-bottom: 1px solid #2d2d2d;
  display: flex;
  align-items: center;
  overflow-x: auto;
}

.editor-tab {
  padding: 8px 16px;
  background-color: transparent;
  border: none;
  color: #cccccc;
  cursor: pointer;
  border-right: 1px solid #2d2d2d;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
}

.editor-tab:hover {
  background-color: #2d2d2d;
}

.editor-tab.active {
  background-color: #1a1a1a;
  color: white;
}

.editor-content {
  flex: 1;
  position: relative;
}

/* File Explorer Styles */
.file-explorer {
  background-color: #1a1a1a;
}

.file-item {
  padding: 6px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  border-radius: 4px;
  margin: 2px 0;
}

.file-item:hover {
  background-color: #2d2d2d;
}

.file-item.active {
  background-color: #007acc;
  color: white;
}

.file-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

/* Preview Styles */
.preview-container {
  background-color: #ffffff;
  border-radius: 4px;
  overflow: hidden;
  height: 100%;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

/* Loading States */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  flex-direction: column;
  gap: 16px;
  color: #cccccc;
}

/* Error States */
.error-container {
  padding: 16px;
  background-color: #2d1b1b;
  border: 1px solid #5c2626;
  border-radius: 6px;
  color: #ff6b6b;
  margin: 16px;
}

/* Success States */
.success-container {
  padding: 16px;
  background-color: #1b2d1b;
  border: 1px solid #266526;
  border-radius: 6px;
  color: #51cf66;
  margin: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 250px;
  }
  
  .chat-interface {
    width: 300px;
  }
  
  .app-body {
    flex-direction: column;
  }
}
