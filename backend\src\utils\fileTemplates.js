class FileTemplates {
  getDefaultScripts(projectType) {
    const scripts = {
      react: {
        "start": "react-scripts start",
        "build": "react-scripts build",
        "test": "react-scripts test",
        "eject": "react-scripts eject"
      },
      nextjs: {
        "dev": "next dev",
        "build": "next build",
        "start": "next start",
        "lint": "next lint"
      },
      vue: {
        "serve": "vue-cli-service serve",
        "build": "vue-cli-service build",
        "lint": "vue-cli-service lint"
      },
      nodejs: {
        "start": "node index.js",
        "dev": "nodemon index.js",
        "test": "jest"
      },
      vanilla: {
        "start": "live-server",
        "build": "webpack --mode production"
      }
    };

    return scripts[projectType] || scripts.react;
  }

  getGitignore(projectType) {
    const common = `# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log`;

    const specific = {
      react: `
# React specific
build/
.eslintcache`,
      nextjs: `
# Next.js specific
.next/
out/
.vercel`,
      vue: `
# Vue specific
dist/`,
      nodejs: `
# Node.js specific
coverage/
.nyc_output`
    };

    return common + (specific[projectType] || '');
  }

  generateReadme(project) {
    return `# ${project.description || 'Generated Project'}

This project was generated using AI code generation.

## Project Type
${project.projectType}

## Getting Started

### Prerequisites
- Node.js (version 14 or higher)
- npm or yarn

### Installation

1. Install dependencies:
\`\`\`bash
npm install
\`\`\`

2. Start the development server:
\`\`\`bash
npm start
\`\`\`

## Available Scripts

${Object.entries(project.scripts || {}).map(([script, command]) => 
  `- \`npm run ${script}\`: ${command}`
).join('\n')}

## Project Structure

\`\`\`
${this.generateProjectStructure(project.files)}
\`\`\`

## Dependencies

### Production Dependencies
${Object.entries(project.dependencies || {}).map(([dep, version]) => 
  `- ${dep}: ${version}`
).join('\n') || 'None'}

### Development Dependencies
${Object.entries(project.devDependencies || {}).map(([dep, version]) => 
  `- ${dep}: ${version}`
).join('\n') || 'None'}

## Instructions

${(project.instructions || []).map((instruction, index) => 
  `${index + 1}. ${instruction}`
).join('\n')}

---

Generated with ❤️ by AI Code Platform`;
  }

  generateProjectStructure(files) {
    if (!files || files.length === 0) return 'No files generated';
    
    return files
      .map(file => file.path)
      .sort()
      .map(path => {
        const depth = (path.match(/\//g) || []).length;
        const indent = '  '.repeat(depth);
        const fileName = path.split('/').pop();
        return `${indent}${fileName}`;
      })
      .join('\n');
  }

  getBasicComponent(projectType) {
    const components = {
      react: `import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to Your Generated App</h1>
        <p>This app was generated using AI!</p>
      </header>
    </div>
  );
}

export default App;`,

      nextjs: `import Head from 'next/head';
import styles from '../styles/Home.module.css';

export default function Home() {
  return (
    <div className={styles.container}>
      <Head>
        <title>Generated Next.js App</title>
        <meta name="description" content="Generated by AI" />
      </Head>

      <main className={styles.main}>
        <h1 className={styles.title}>
          Welcome to your Generated App!
        </h1>
        <p className={styles.description}>
          This app was generated using AI
        </p>
      </main>
    </div>
  );
}`,

      vue: `<template>
  <div id="app">
    <header>
      <h1>Welcome to Your Generated Vue App</h1>
      <p>This app was generated using AI!</p>
    </header>
  </div>
</template>

<script>
export default {
  name: 'App',
  components: {}
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>`,

      nodejs: `const express = require('express');
const app = express();
const PORT = process.env.PORT || 3000;

app.use(express.json());

app.get('/', (req, res) => {
  res.json({ 
    message: 'Welcome to your generated API!',
    timestamp: new Date().toISOString()
  });
});

app.listen(PORT, () => {
  console.log(\`Server running on port \${PORT}\`);
});`,

      vanilla: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated App</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            text-align: center;
            margin-top: 50px;
        }
    </style>
</head>
<body>
    <h1>Welcome to Your Generated App</h1>
    <p>This app was generated using AI!</p>
    
    <script>
        console.log('Generated app loaded successfully!');
    </script>
</body>
</html>`
    };

    return components[projectType] || components.react;
  }
}

module.exports = new FileTemplates();
