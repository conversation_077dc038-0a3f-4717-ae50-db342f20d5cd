class ProjectController {
  constructor() {
    // In-memory storage for projects (since no database)
    this.projects = new Map();
    this.projectCounter = 1;
  }

  async createProject(req, res) {
    try {
      const { name, description, files, projectType } = req.body;

      if (!name || !files) {
        return res.status(400).json({
          error: 'Project name and files are required'
        });
      }

      const projectId = `project_${this.projectCounter++}`;
      const project = {
        id: projectId,
        name,
        description: description || '',
        projectType: projectType || 'react',
        files: files || [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      this.projects.set(projectId, project);

      console.log(`Created project: ${projectId}`);

      res.status(201).json({
        success: true,
        project,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Project creation error:', error);
      res.status(500).json({
        error: 'Failed to create project',
        message: error.message
      });
    }
  }

  async getProject(req, res) {
    try {
      const { projectId } = req.params;

      if (!this.projects.has(projectId)) {
        return res.status(404).json({
          error: 'Project not found',
          message: `Project with ID ${projectId} does not exist`
        });
      }

      const project = this.projects.get(projectId);

      res.json({
        success: true,
        project,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Get project error:', error);
      res.status(500).json({
        error: 'Failed to retrieve project',
        message: error.message
      });
    }
  }

  async updateProject(req, res) {
    try {
      const { projectId } = req.params;
      const updates = req.body;

      if (!this.projects.has(projectId)) {
        return res.status(404).json({
          error: 'Project not found',
          message: `Project with ID ${projectId} does not exist`
        });
      }

      const project = this.projects.get(projectId);
      const updatedProject = {
        ...project,
        ...updates,
        id: projectId, // Ensure ID cannot be changed
        updatedAt: new Date().toISOString()
      };

      this.projects.set(projectId, updatedProject);

      console.log(`Updated project: ${projectId}`);

      res.json({
        success: true,
        project: updatedProject,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Project update error:', error);
      res.status(500).json({
        error: 'Failed to update project',
        message: error.message
      });
    }
  }

  async deleteProject(req, res) {
    try {
      const { projectId } = req.params;

      if (!this.projects.has(projectId)) {
        return res.status(404).json({
          error: 'Project not found',
          message: `Project with ID ${projectId} does not exist`
        });
      }

      this.projects.delete(projectId);

      console.log(`Deleted project: ${projectId}`);

      res.json({
        success: true,
        message: 'Project deleted successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Project deletion error:', error);
      res.status(500).json({
        error: 'Failed to delete project',
        message: error.message
      });
    }
  }

  async listProjects(req, res) {
    try {
      const projects = Array.from(this.projects.values())
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      res.json({
        success: true,
        projects,
        count: projects.length,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('List projects error:', error);
      res.status(500).json({
        error: 'Failed to list projects',
        message: error.message
      });
    }
  }

  async updateFile(req, res) {
    try {
      const { projectId } = req.params;
      const { filePath, content } = req.body;

      if (!filePath || content === undefined) {
        return res.status(400).json({
          error: 'File path and content are required'
        });
      }

      if (!this.projects.has(projectId)) {
        return res.status(404).json({
          error: 'Project not found'
        });
      }

      const project = this.projects.get(projectId);
      const fileIndex = project.files.findIndex(f => f.path === filePath);

      if (fileIndex === -1) {
        // Add new file
        project.files.push({
          path: filePath,
          content,
          type: 'file'
        });
      } else {
        // Update existing file
        project.files[fileIndex].content = content;
      }

      project.updatedAt = new Date().toISOString();
      this.projects.set(projectId, project);

      console.log(`Updated file ${filePath} in project ${projectId}`);

      res.json({
        success: true,
        message: 'File updated successfully',
        file: { path: filePath, content },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('File update error:', error);
      res.status(500).json({
        error: 'Failed to update file',
        message: error.message
      });
    }
  }

  async deleteFile(req, res) {
    try {
      const { projectId } = req.params;
      const { filePath } = req.body;

      if (!filePath) {
        return res.status(400).json({
          error: 'File path is required'
        });
      }

      if (!this.projects.has(projectId)) {
        return res.status(404).json({
          error: 'Project not found'
        });
      }

      const project = this.projects.get(projectId);
      const fileIndex = project.files.findIndex(f => f.path === filePath);

      if (fileIndex === -1) {
        return res.status(404).json({
          error: 'File not found'
        });
      }

      project.files.splice(fileIndex, 1);
      project.updatedAt = new Date().toISOString();
      this.projects.set(projectId, project);

      console.log(`Deleted file ${filePath} from project ${projectId}`);

      res.json({
        success: true,
        message: 'File deleted successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('File deletion error:', error);
      res.status(500).json({
        error: 'Failed to delete file',
        message: error.message
      });
    }
  }
}

module.exports = new ProjectController();
