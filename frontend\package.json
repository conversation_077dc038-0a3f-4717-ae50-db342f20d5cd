{"name": "ai-code-platform-frontend", "version": "0.1.0", "private": true, "dependencies": {"@webcontainer/api": "^1.1.9", "@monaco-editor/react": "^4.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "axios": "^1.6.2", "lucide-react": "^0.294.0", "react-split-pane": "^0.1.92", "react-hotkeys-hook": "^4.4.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18"}}