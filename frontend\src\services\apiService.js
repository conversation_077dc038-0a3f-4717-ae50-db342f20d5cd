import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000, // 30 seconds timeout for AI requests
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        return response.data;
      },
      (error) => {
        console.error('Response error:', error);
        
        if (error.response) {
          // Server responded with error status
          const message = error.response.data?.message || error.response.data?.error || 'Server error';
          throw new Error(message);
        } else if (error.request) {
          // Request was made but no response received
          throw new Error('No response from server. Please check your connection.');
        } else {
          // Something else happened
          throw new Error(error.message || 'An unexpected error occurred');
        }
      }
    );
  }

  // AI Generation Methods
  async generateProject(projectData) {
    try {
      const response = await this.client.post('/ai/generate-project', projectData);
      return response;
    } catch (error) {
      console.error('Project generation failed:', error);
      throw error;
    }
  }

  async generateFile(prompt, fileType = 'component') {
    try {
      const response = await this.client.post('/ai/generate-file', {
        prompt,
        fileType
      });
      return response;
    } catch (error) {
      console.error('File generation failed:', error);
      throw error;
    }
  }

  async enhanceCode(code, enhancement) {
    try {
      const response = await this.client.post('/ai/enhance-code', {
        code,
        enhancement
      });
      return response;
    } catch (error) {
      console.error('Code enhancement failed:', error);
      throw error;
    }
  }

  async explainCode(code) {
    try {
      const response = await this.client.post('/ai/explain-code', {
        code
      });
      return response;
    } catch (error) {
      console.error('Code explanation failed:', error);
      throw error;
    }
  }

  async getSupportedFrameworks() {
    try {
      const response = await this.client.get('/ai/frameworks');
      return response;
    } catch (error) {
      console.error('Failed to fetch frameworks:', error);
      throw error;
    }
  }

  // Project Management Methods
  async createProject(projectData) {
    try {
      const response = await this.client.post('/projects', projectData);
      return response;
    } catch (error) {
      console.error('Project creation failed:', error);
      throw error;
    }
  }

  async getProject(projectId) {
    try {
      const response = await this.client.get(`/projects/${projectId}`);
      return response;
    } catch (error) {
      console.error('Failed to fetch project:', error);
      throw error;
    }
  }

  async updateProject(projectId, updates) {
    try {
      const response = await this.client.put(`/projects/${projectId}`, updates);
      return response;
    } catch (error) {
      console.error('Project update failed:', error);
      throw error;
    }
  }

  async deleteProject(projectId) {
    try {
      const response = await this.client.delete(`/projects/${projectId}`);
      return response;
    } catch (error) {
      console.error('Project deletion failed:', error);
      throw error;
    }
  }

  async listProjects() {
    try {
      const response = await this.client.get('/projects');
      return response;
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      throw error;
    }
  }

  async updateFile(projectId, filePath, content) {
    try {
      const response = await this.client.put(`/projects/${projectId}/files`, {
        filePath,
        content
      });
      return response;
    } catch (error) {
      console.error('File update failed:', error);
      throw error;
    }
  }

  async deleteFile(projectId, filePath) {
    try {
      const response = await this.client.delete(`/projects/${projectId}/files`, {
        data: { filePath }
      });
      return response;
    } catch (error) {
      console.error('File deletion failed:', error);
      throw error;
    }
  }

  // Health Check
  async healthCheck() {
    try {
      const response = await this.client.get('/health');
      return response;
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  }
}

export default new ApiService();
