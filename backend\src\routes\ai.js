const express = require('express');
const aiController = require('../controllers/aiController');

const router = express.Router();

// Generate a complete project
router.post('/generate-project', aiController.generateProject);

// Generate a single file
router.post('/generate-file', aiController.generateFile);

// Enhance existing code
router.post('/enhance-code', aiController.enhanceCode);

// Explain code
router.post('/explain-code', aiController.explainCode);

// Get supported frameworks and options
router.get('/frameworks', aiController.getSupportedFrameworks);

module.exports = router;
