import React, { useState } from 'react';
import CodeEditor from './CodeEditor';
import PreviewFrame from './PreviewFrame';
import { useProject } from '../../context/ProjectContext';
import { Eye, Code, Split } from 'lucide-react';

function MainEditor() {
  const { activeFile, files, previewUrl } = useProject();
  const [viewMode, setViewMode] = useState('split'); // 'code', 'preview', 'split'

  if (files.length === 0) {
    return (
      <div className="editor-container">
        <div className="loading-container">
          <div className="text-lg font-bold">Welcome to AI Code Platform</div>
          <div className="text-sm" style={{ color: '#888' }}>
            Start by chatting with AI to generate your project, or add files manually.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="editor-container">
      {/* Editor Tabs */}
      <div className="editor-tabs">
        <div className="flex items-center gap-2 flex-1">
          {files.map((file) => (
            <FileTab key={file.path} file={file} />
          ))}
        </div>
        
        <div className="flex items-center gap-1 mr-4">
          <button
            className={`btn ${viewMode === 'code' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setViewMode('code')}
            title="Code Only"
          >
            <Code size={14} />
          </button>
          <button
            className={`btn ${viewMode === 'split' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setViewMode('split')}
            title="Split View"
          >
            <Split size={14} />
          </button>
          <button
            className={`btn ${viewMode === 'preview' ? 'btn-primary' : 'btn-secondary'}`}
            onClick={() => setViewMode('preview')}
            title="Preview Only"
          >
            <Eye size={14} />
          </button>
        </div>
      </div>

      {/* Editor Content */}
      <div className="editor-content">
        {viewMode === 'code' && (
          <CodeEditor file={activeFile} />
        )}
        
        {viewMode === 'preview' && (
          <PreviewFrame url={previewUrl} />
        )}
        
        {viewMode === 'split' && (
          <div style={{ display: 'flex', height: '100%' }}>
            <div style={{ flex: 1, borderRight: '1px solid #2d2d2d' }}>
              <CodeEditor file={activeFile} />
            </div>
            <div style={{ flex: 1 }}>
              <PreviewFrame url={previewUrl} />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function FileTab({ file }) {
  const { activeFile, setActiveFile, removeFile } = useProject();
  const isActive = activeFile?.path === file.path;

  const handleClick = () => {
    setActiveFile(file);
  };

  const handleClose = (e) => {
    e.stopPropagation();
    if (window.confirm(`Close ${file.path}?`)) {
      removeFile(file.path);
    }
  };

  return (
    <div
      className={`editor-tab ${isActive ? 'active' : ''}`}
      onClick={handleClick}
    >
      <span>{file.path.split('/').pop()}</span>
      <button
        onClick={handleClose}
        style={{ 
          background: 'none', 
          border: 'none', 
          color: 'inherit', 
          cursor: 'pointer',
          padding: '2px',
          borderRadius: '2px'
        }}
        onMouseEnter={(e) => e.target.style.backgroundColor = '#ff4444'}
        onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
      >
        ×
      </button>
    </div>
  );
}

export default MainEditor;
