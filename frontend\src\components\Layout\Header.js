import React from 'react';
import { Menu, MessageSquare, Play, Download, Settings } from 'lucide-react';

function Header({ onToggleSidebar, onToggleChat, sidebarOpen, chatOpen }) {
  return (
    <div className="header">
      <div className="header-left">
        <button 
          className="btn btn-secondary"
          onClick={onToggleSidebar}
          title="Toggle Sidebar"
        >
          <Menu size={16} />
        </button>
        
        <div className="logo">
          AI Code Platform
        </div>
      </div>

      <div className="header-center">
        <div className="flex items-center gap-2">
          <button className="btn btn-primary">
            <Play size={16} />
            Run Project
          </button>
          
          <button className="btn btn-secondary">
            <Download size={16} />
            Export
          </button>
        </div>
      </div>

      <div className="header-right">
        <button className="btn btn-secondary" title="Settings">
          <Settings size={16} />
        </button>
        
        <button 
          className="btn btn-secondary"
          onClick={onToggleChat}
          title="Toggle AI Chat"
        >
          <MessageSquare size={16} />
          {!chatOpen && <span className="text-sm">AI</span>}
        </button>
      </div>
    </div>
  );
}

export default Header;
